
<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="terminal/terminal-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getColumn"
          :auto-load="false"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="证件柜ID">
                <el-input
                  style="width: 200px;margin:0 10px 0 0;"
                  v-model.trim="searchForm.icbId"
                  size="small"
                  placeholder="请输入证件柜ID"
                ></el-input>
              </el-form-item>
              <el-form-item label="IP地址">
                <el-input
                  style="width: 200px;margin:0 10px 0 0;"
                  v-model.trim="searchForm.ipAddress"
                  size="small"
                  placeholder="请输入IP地址"
                ></el-input>
              </el-form-item>
              <el-form-item label="证件柜名称">
                <el-input
                  style="width: 200px;margin:0 10px 0 0;"
                  v-model.trim="searchForm.icbName"
                  size="small"
                  placeholder="请输入证件柜名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="状态">
                <el-select
                  size="small"
                  clearable
                  @keydown.enter.native.prevent="searchTable"
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                >
                  <el-option label="在线" value="1"></el-option>
                  <el-option label="离线" value="0"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="分配状态">
                <el-select
                  size="small"
                  clearable
                  @keydown.enter.native.prevent="searchTable"
                  v-model="searchForm.distributeStatus"
                  placeholder="请选择分配状态"
                >
                  <el-option label="已分配" value="1"></el-option>
                  <el-option label="待分配" value="0"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button>
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <el-table slot="table" slot-scope="{}" v-loading="tableLoading" ref="multipleTable" :data="tableData" stripe style="width: 100%">
            <el-table-column fixed="left" :align="'center'" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot === 'status'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="100"
              >
                <template slot-scope="scope">
                  <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
                    {{ scope.row.status === '1' ? '在线' : '离线' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                v-else-if="item.slot === 'distributeStatus'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="100"
              >
                <template slot-scope="scope">
                  <el-tag :type="scope.row.distributeStatus === 'assigned' ? 'success' : 'warning'">
                    {{ scope.row.distributeStatus === '1' ? '已分配' : '待分配' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="200"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.distributeStatus === '1'"
                  style="margin-right:6px"
                  @click="handleChangeTenant(scope.row)"
                  type="text"
                  size="small"
                  v-permission="'terminal_admin_change'"
                  >更改租户</el-button>
                <el-button
                  v-if="scope.row.distributeStatus === '0'"
                  style="margin-right:6px"
                  @click="handleAssignTenant(scope.row)"
                  type="text"
                  size="small"
                  v-permission="'terminal_admin_assign'"
                  >分配租户</el-button>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>

    <!-- 分配租户弹窗 -->
    <AssignTenantDialog
      :visible.sync="assignDialogVisible"
      :terminal-info="currentTerminal"
      :tenant-list="tenantList"
      :loading="submitLoading"
      @confirm="handleAssignConfirm"
      @close="handleAssignClose"
    />

    <!-- 更改租户弹窗 -->
    <ChangeTenantDialog
      :visible.sync="changeDialogVisible"
      :terminal-info="currentTerminal"
      :tenant-list="tenantList"
      :loading="submitLoading"
      @confirm="handleChangeConfirm"
      @close="handleChangeClose"
    />
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';

import AssignTenantDialog from './components/AssignTenantDialog.vue';
import ChangeTenantDialog from './components/ChangeTenantDialog.vue';

const defaultSearchForm = {
  icbId: '',
  ipAddress: '',
  icbName: '',
  status: '',
  distributeStatus: ''
};

export default {
  components: {
    Grid,
    AssignTenantDialog,
    ChangeTenantDialog
  },
  destroyed() {
    this.searchEventBus.$off();

    // 清理遮罩层观察器
    if (this.maskObserver) {
      this.maskObserver.disconnect();
    }
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      submitLoading: false,
      tableLoading: false,
      searchForm: Object.assign({}, defaultSearchForm),
      columns: [
        {
          title: '证件柜ID',
          key: 'icbId',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: 'IP地址',
          key: 'ipAddress',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '证件柜名称',
          key: 'icbName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '上线时间',
          key: 'onlineTime',
          tooltip: true,
          minWidth: 180,
        },
        {
          title: '状态',
          key: 'status',
          slot: 'status',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '分配状态',
          key: 'distributeStatus',
          slot: 'distributeStatus',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '当前租户',
          key: 'currentTenant',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
      // 弹窗相关
      assignDialogVisible: false,
      changeDialogVisible: false,
      tenantList: [], // 租户列表
      currentTerminal: {} // 当前操作的终端
    };
  },
  mounted() {
    // 确保页面加载时重置所有loading状态
    this.tableLoading = false;
  },

  methods: {
    // 获取数据时的回调
    getDatas(data) {
      this.tableData = data;
      this.tableLoading = false; // 确保数据加载完成后隐藏loading
    },

    // 分配租户
    handleAssignTenant(row) {
      this.currentTerminal = Object.assign({}, row);
      this.assignDialogVisible = true;
    },

    // 更改租户
    handleChangeTenant(row) {
      this.currentTerminal = Object.assign({}, row);
      this.changeDialogVisible = true;
    },

    // 分配租户确认
    handleAssignConfirm(data) {
      this.submitLoading = true;

      // 调用 terminal-save 接口
      const params = {
        icbId: data.icbId,
        distributeTenantId: data.tenantId,
        distributeStatus: '1' // 设置为已分配状态
      };

      this.$api["terminal/terminal-save"](params).then(() => {
        // 更新本地数据
        const terminal = this.tableData.find(item => item.icbId === data.icbId);
        if (terminal) {
          const tenant = this.tenantList.find(t => t.id === data.tenantId);
          terminal.distributeStatus = '1';
          terminal.currentTenant = tenant ? tenant.tenantName : '';
          terminal.distributeTenantId = data.tenantId;
        }

        this.$message({
          message: '分配成功',
          type: 'success'
        });
        this.assignDialogVisible = false;
        this.submitLoading = false;
      }).catch(error => {
        console.error('分配租户失败:', error);
        this.$message({
          message: '分配失败，请重试',
          type: 'error'
        });
        this.submitLoading = false;
      });
    },

    // 更改租户确认
    handleChangeConfirm(data) {
      this.submitLoading = true;

      // 调用 terminal-save 接口
      const params = {
        id: data.icbId, // 传递id参数
        icbId: data.icbId,
        distributeTenantId: data.tenantId,
        distributeStatus: '1' // 保持已分配状态
      };

      this.$api["terminal/terminal-save"](params).then(() => {
        // 更新本地数据
        const terminal = this.tableData.find(item => item.icbId === data.icbId);
        if (terminal) {
          const tenant = this.tenantList.find(t => t.id === data.tenantId);
          terminal.currentTenant = tenant ? tenant.tenantName : '';
          terminal.distributeTenantId = data.tenantId;
        }

        this.$message({
          message: '更改成功',
          type: 'success'
        });
        this.changeDialogVisible = false;
        this.submitLoading = false;
      }).catch(error => {
        console.error('更改租户失败:', error);
        this.$message({
          message: '更改失败，请重试',
          type: 'error'
        });
        this.submitLoading = false;
      });
    },

    // 分配弹窗关闭
    handleAssignClose() {
      this.currentTerminal = {};
    },

    // 更改弹窗关闭
    handleChangeClose() {
      this.currentTerminal = {};
    },

    searchTable() {
      this.$refs.grid.query();
    },

    resetTable() {
      this.searchForm = Object.assign({}, defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },

    getColumn(e) {
      this.columns = e;
    },

    // 加载测试数据
    loadTestData() {
      this.tableData = [
        {
          id: 1,
          icbId: 'T0001',
          ipAddress: '***********',
          icbName: '测试柜1',
          onlineTime: '2025-07-29',
          status: 'online',
          distributeStatus: '1',
          currentTenant: '租户A',
          distributeTenantId: 1
        },
        {
          id: 2,
          icbId: 'T0002',
          ipAddress: '***********',
          icbName: '测试柜2',
          onlineTime: '2025-07-29',
          status: 'online',
          distributeStatus: '0',
          currentTenant: '',
          distributeTenantId: null
        }
      ];

      // 模拟租户列表数据
      this.tenantList = [
        {id: 1, tenantName: '租户A'},
        {id: 2, tenantName: '租户B'},
        {id: 3, tenantName: '租户C'}
      ];
    }
  },
};
</script>
<style lang="less" scoped></style>

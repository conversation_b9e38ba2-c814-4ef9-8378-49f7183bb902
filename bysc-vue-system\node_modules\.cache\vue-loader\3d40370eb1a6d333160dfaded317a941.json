{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue?vue&type=style&index=0&id=60b06436&lang=less&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue", "mtime": 1753931111230}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\css-loader\\index.js", "mtime": 1745221300128}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745221314654}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745221303798}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745221307121}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n.admin-manage-container {\n  padding: 20px;\n  height: calc(100vh - 120px); // 最大化利用屏幕高度\n  display: flex;\n  flex-direction: column;\n\n  .action-bar {\n    margin-bottom: 16px;\n    padding-bottom: 16px;\n    border-bottom: 1px solid #ebeef5;\n    flex-shrink: 0; // 不缩放\n  }\n\n  .el-table {\n    border: 1px solid #ebeef5;\n    flex: 1; // 占据剩余空间\n    overflow: auto;\n  }\n\n  .pagination-container {\n    margin-top: 16px;\n    padding-top: 16px;\n    border-top: 1px solid #ebeef5;\n    text-align: right;\n    flex-shrink: 0; // 不缩放\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n  padding-top: 16px;\n  border-top: 1px solid #ebeef5;\n}\n\n// 抽屉样式优化\n:deep(.el-drawer) {\n  .el-drawer__header {\n    padding: 20px 20px 0 20px;\n    margin-bottom: 0;\n  }\n\n  .el-drawer__body {\n    padding: 0;\n    overflow-y: auto;\n  }\n}\n", {"version": 3, "sources": ["AdminManageDialog.vue"], "names": [], "mappings": ";AAgfA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "AdminManageDialog.vue", "sourceRoot": "src/bysc_system/views/tenant/components", "sourcesContent": ["\n<template>\n  <el-drawer\n    title=\"维护管理员\"\n    :visible.sync=\"dialogVisible\"\n    direction=\"rtl\"\n    size=\"80%\"\n    :close-on-press-escape=\"false\"\n    :wrapperClosable=\"false\"\n    @close=\"handleClose\"\n  >\n    <div class=\"admin-manage-container\">\n      <!-- 操作按钮区域 -->\n      <div class=\"action-bar\">\n        <el-button type=\"primary\" size=\"small\" @click=\"handleAdd\">添加</el-button>\n      </div>\n\n      <!-- 管理员列表表格 -->\n      <el-table\n        :data=\"currentPageData\"\n        stripe\n        style=\"width: 100%\"\n        v-loading=\"tableLoading\"\n        height=\"400\"\n        border\n      >\n        <el-table-column\n          prop=\"account\"\n          label=\"用户名\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"nickname\"\n          label=\"昵称\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"mobile\"\n          label=\"电话\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          label=\"角色\"\n          width=\"150\"\n          align=\"center\"\n        >\n          <template>\n            租户管理员\n          </template>\n        </el-table-column>\n        <el-table-column\n          prop=\"organizationName\"\n          label=\"组织\"\n          min-width=\"200\"\n          align=\"center\"\n        />\n        <el-table-column\n          label=\"操作\"\n          width=\"250\"\n          align=\"center\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              style=\"margin-right: 10px;\"\n              @click=\"handleEdit(scope.row)\"\n            >\n              修改\n            </el-button>\n            <el-popconfirm\n              title=\"确定要删除该管理员吗？\"\n              @confirm=\"handleDelete(scope.row)\"\n            >\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                style=\"color: #f56c6c\"\n                slot=\"reference\"\n              >\n                删除\n              </el-button>\n            </el-popconfirm>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页组件 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"pagination.currentPage\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :page-size=\"pagination.pageSize\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"pagination.total\"\n        />\n      </div>\n    </div>\n\n    <!-- 添加/编辑管理员表单弹窗 -->\n    <el-dialog\n      :title=\"formTitle\"\n      :visible.sync=\"formDialogVisible\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      append-to-body\n    >\n      <el-form\n        :model=\"adminForm\"\n        :rules=\"formRules\"\n        ref=\"adminForm\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"用户名\" prop=\"account\">\n          <el-input\n            v-model=\"adminForm.account\"\n            placeholder=\"请输入用户名\"\n            :disabled=\"isEdit\"\n          />\n        </el-form-item>\n        <el-form-item label=\"昵称\" prop=\"nickname\">\n          <el-input\n            v-model=\"adminForm.nickname\"\n            placeholder=\"请输入昵称\"\n          />\n        </el-form-item>\n\n\n        <!-- 添加模式下显示的字段 -->\n        <template v-if=\"!isEdit\">\n\n            <el-form-item label=\"电话\" prop=\"mobile\">\n          <el-input\n            v-model=\"adminForm.mobile\"\n            placeholder=\"请输入电话\"\n            maxlength=\"11\"\n\n          />\n        </el-form-item>\n\n          <el-form-item label=\"密码\" prop=\"password\">\n            <el-input\n              v-model=\"adminForm.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              show-password\n            />\n          </el-form-item>\n          <el-form-item label=\"再次确认密码\" prop=\"confirmPassword\">\n            <el-input\n              v-model=\"adminForm.confirmPassword\"\n              type=\"password\"\n              placeholder=\"请再次输入密码\"\n              show-password\n            />\n          </el-form-item>\n          <el-form-item label=\"角色\" prop=\"roleId\">\n            <el-select\n              v-model=\"adminForm.roleId\"\n              placeholder=\"请选择角色\"\n              style=\"width: 100%\"\n              disabled\n            >\n              <el-option\n                v-for=\"role in roleList\"\n                :key=\"role.id\"\n                :label=\"role.name\"\n                :value=\"role.id\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"组织\" prop=\"organizationId\">\n            <el-select\n              v-model=\"adminForm.organizationId\"\n              placeholder=\"请选择组织\"\n              style=\"width: 100%\"\n              disabled\n            >\n              <el-option\n                v-for=\"org in orgList\"\n                :key=\"org.id\"\n                :label=\"org.name\"\n                :value=\"org.id\"\n              />\n            </el-select>\n          </el-form-item>\n        </template>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleFormCancel\">取消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleFormSubmit\"\n          :loading=\"formLoading\"\n        >\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </el-drawer>\n</template>\n\n<script>\nexport default {\n  name: 'AdminManageDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    tenantInfo: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      formDialogVisible: false,\n      tableLoading: false,\n      formLoading: false,\n      isEdit: false,\n      formTitle: '添加管理员',\n      // 分页相关数据\n      pagination: {\n        currentPage: 1,\n        pageSize: 10,\n        total: 0\n      },\n      adminList: [],\n      roleList: [],\n      orgList: [\n      ],\n      adminForm: {\n        id: null,\n        account: '',\n        nickname: '',\n        mobile: '',\n        password: '',\n        confirmPassword: '',\n        roleId: null,\n        organizationId: null,\n        tenantId: null,\n        organizationName: ''\n      },\n      formRules: {\n        account: [\n          {required: true, message: '请输入用户名', trigger: 'blur'},\n          {\n            min: 3,\n            max: 16,\n            message: '账号长度为 3 到 16 个字符',\n            trigger: 'blur'\n          },\n          {\n            pattern: /^[a-z0-9_-]+$/,\n            message: '账号格式不正确，只能包含小写字母、数字、下划线 _、连字符 -',\n            trigger: 'blur'\n          }\n        ],\n        nickname: [\n          {required: true, message: '请输入昵称', trigger: 'blur'}\n        ],\n        mobile: [\n          {required: true, message: '请输入电话', trigger: 'blur'},\n          {\n            pattern: /^1[3-9]\\d{9}$/,\n            message: '请输入正确的电话格式',\n            trigger: 'blur'\n          }\n        ],\n        password: [\n          {required: true, message: '请输入密码', trigger: 'blur'},\n          {min: 6, message: '密码长度不能少于6位', trigger: 'blur'}\n        ],\n        confirmPassword: [\n          {required: true, message: '请再次输入密码', trigger: 'blur'},\n          {\n            validator: (rule, value, callback) => {\n              if (value !== this.adminForm.password) {\n                callback(new Error('两次输入的密码不一致'));\n              } else {\n                callback();\n              }\n            },\n            trigger: 'blur'\n          }\n        ],\n        roleId: [\n          {required: true, message: '请选择角色', trigger: 'change'}\n        ],\n        organizationId: [\n          {required: true, message: '请选择组织', trigger: 'change'}\n        ]\n      }\n    };\n  },\n  computed: {\n    // 当前页显示的数据（现在直接返回adminList，因为后端已经分页）\n    currentPageData() {\n      return this.adminList;\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        this.loadAdminList();\n        this.loadRoleList();\n        this.loadOrgList();\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    // 加载管理员列表\n    loadAdminList() {\n      if (!this.tenantInfo.id) {\n        return;\n      }\n\n      this.tableLoading = true;\n      // 调用真实API接口\n      this.$api['tenant/baseTenantAdmin-page']({\n        current: this.pagination.currentPage,\n        limit: this.pagination.pageSize,\n        param: {\n          tenantId: this.tenantInfo.id\n        }\n      }).then(res => {\n        if (res) {\n          this.adminList = res.list || [];\n          this.pagination.total = res.total || 0;\n        }\n      }).catch(error => {\n        console.error('加载管理员列表失败:', error);\n        this.$message.error('加载管理员列表失败');\n        this.adminList = [];\n        this.pagination.total = 0;\n      }).finally(() => {\n        this.tableLoading = false;\n      });\n    },\n\n    // 加载角色列表\n    loadRoleList() {\n      // 设置固定的租户管理员角色\n      this.roleList = [\n        {\n          id: 'tenant_admin',\n          name: '租户管理员',\n          code: 'TENANT_ADMIN'\n        }\n      ];\n      console.log('角色列表已加载:', this.roleList);\n    },\n\n    // 加载组织列表\n    loadOrgList() {\n      // 使用来自 tenantInfo 的组织信息\n      if (this.tenantInfo.organizationName && this.tenantInfo.organizationId) {\n        this.orgList = [\n          {\n            id: this.tenantInfo.organizationId,\n            name: this.tenantInfo.organizationName,\n            code: 'tenant_org'\n          }\n        ];\n      }\n      console.log('组织列表已加载:', this.orgList);\n    },\n\n    // 添加管理员\n    handleAdd() {\n      this.isEdit = false;\n      this.formTitle = '添加管理员';\n\n      // 设置默认的角色和组织\n      const defaultRoleId = this.roleList.length > 0 ? this.roleList[0].id : null;\n      const defaultorganizationId = this.orgList.length > 0 ? this.orgList[0].id : null;\n      const defaultOrgName = this.orgList.length > 0 ? this.orgList[0].name : '';\n\n      this.adminForm = {\n        id: null,\n        account: '',\n        nickname: '',\n        mobile: '',\n        password: '',\n        confirmPassword: '',\n        roleId: defaultRoleId, // 默认选择租户管理员\n        organizationId: this.tenantInfo.organizationId || defaultorganizationId,\n        tenantId: this.tenantInfo.id,\n        organizationName: this.tenantInfo.organizationName || defaultOrgName\n      };\n      this.formDialogVisible = true;\n    },\n\n    // 编辑管理员\n    handleEdit(row) {\n      this.isEdit = true;\n      this.formTitle = '编辑管理员';\n      this.adminForm = {\n        id: row.id,\n        account: row.account,\n        nickname: row.nickname,\n        mobile: row.mobile || '',\n        password: '', // 编辑时不显示密码字段\n        confirmPassword: '',\n        roleId: row.roleId,\n        organizationId: row.organizationId || this.tenantInfo.organizationId || null,\n        tenantId: this.tenantInfo.id,\n        organizationName: row.organizationName || this.tenantInfo.organizationName || ''\n      };\n      this.formDialogVisible = true;\n    },\n\n    // 删除管理员\n    handleDelete(row) {\n      // 调用真实API接口删除\n      this.$api['tenant/baseTenantAdmin-delete']({\n        id: row.id\n      }).then(() => {\n        this.$message.success('删除成功');\n        // 重新加载列表\n        this.loadAdminList();\n      }).catch(error => {\n        console.error('删除管理员失败:', error);\n        this.$message.error(error);\n      });\n    },\n\n    // 表单提交\n    handleFormSubmit() {\n      this.$refs.adminForm.validate(valid => {\n        if (valid) {\n          this.formLoading = true;\n\n          // 准备提交的数据，确保包含 organizationName 和 organizationId\n          const submitData = {\n            ...this.adminForm,\n            organizationName: this.adminForm.organizationName || this.tenantInfo.organizationName || '',\n            organizationId: this.adminForm.organizationId || this.tenantInfo.organizationId || null\n          };\n\n          console.log('提交的管理员数据:', submitData);\n\n          // 调用真实API接口保存\n          this.$api['tenant/baseTenantAdmin-save'](submitData).then(() => {\n            this.$message.success(this.isEdit ? '修改成功' : '添加成功');\n            this.formDialogVisible = false;\n            // 重新加载列表\n            this.loadAdminList();\n          }).catch(error => {\n            console.error('保存管理员失败:', error);\n            this.$message.error('保存失败');\n          }).finally(() => {\n            this.formLoading = false;\n          });\n        }\n      });\n    },\n\n    // 表单取消\n    handleFormCancel() {\n      this.formDialogVisible = false;\n      this.$refs.adminForm.resetFields();\n    },\n\n    // 关闭主弹窗\n    handleClose() {\n      this.dialogVisible = false;\n    },\n\n    // 分页相关方法\n    handleSizeChange(val) {\n      this.pagination.pageSize = val;\n      this.pagination.currentPage = 1; // 重置到第一页\n      this.loadAdminList(); // 重新加载数据\n    },\n\n    handleCurrentChange(val) {\n      this.pagination.currentPage = val;\n      this.loadAdminList(); // 重新加载数据\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.admin-manage-container {\n  padding: 20px;\n  height: calc(100vh - 120px); // 最大化利用屏幕高度\n  display: flex;\n  flex-direction: column;\n\n  .action-bar {\n    margin-bottom: 16px;\n    padding-bottom: 16px;\n    border-bottom: 1px solid #ebeef5;\n    flex-shrink: 0; // 不缩放\n  }\n\n  .el-table {\n    border: 1px solid #ebeef5;\n    flex: 1; // 占据剩余空间\n    overflow: auto;\n  }\n\n  .pagination-container {\n    margin-top: 16px;\n    padding-top: 16px;\n    border-top: 1px solid #ebeef5;\n    text-align: right;\n    flex-shrink: 0; // 不缩放\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n  padding-top: 16px;\n  border-top: 1px solid #ebeef5;\n}\n\n// 抽屉样式优化\n:deep(.el-drawer) {\n  .el-drawer__header {\n    padding: 20px 20px 0 20px;\n    margin-bottom: 0;\n  }\n\n  .el-drawer__body {\n    padding: 0;\n    overflow-y: auto;\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\tenant.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\tenant.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["export default [{\n  name: 'tenant-page',\n  // 租户分页\n  method: 'POST',\n  path: '/base/tenant/page'\n}, {\n  name: 'tenant-page',\n  // 租户分页\n  method: 'POST',\n  path: '/base/tenant/page'\n}, {\n  name: 'switch-tenantstatus',\n  method: 'POST',\n  path: '/base/tenant/switch-status'\n}, {\n  name: 'tenant-save',\n  // 保存\n  method: 'POST',\n  path: '/base/tenant/save'\n}, {\n  name: 'tenant-delete',\n  // 删除\n  method: 'POST',\n  path: '/base/tenant/delete'\n}, {\n  name: 'baseTenantAdmin-page',\n  // 租户管理员表分页\n  method: 'POST',\n  path: '/base/baseTenantAdmin/page'\n}, {\n  name: 'baseTenantAdmin-save',\n  // 保存租户管理员表\n  method: 'POST',\n  path: '/base/baseTenantAdmin/save'\n}, {\n  name: 'baseTenantAdmin-delete',\n  // 删除\n  method: 'OTHER',\n  path: '/base/baseTenantAdmin/delete'\n}];", {"version": 3, "names": ["name", "method", "path"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/service/api/account/tenant.js"], "sourcesContent": ["export default [\r\n  {\r\n    name: 'tenant-page', // 租户分页\r\n    method: 'POST',\r\n    path: '/base/tenant/page'\r\n  },\r\n\r\n  {\r\n    name: 'tenant-page', // 租户分页\r\n    method: 'POST',\r\n    path: '/base/tenant/page'\r\n  },\r\n  {\r\n    name: 'switch-tenantstatus',\r\n    method: 'POST',\r\n    path: '/base/tenant/switch-status'\r\n  },\r\n  {\r\n    name: 'tenant-save', // 保存\r\n    method: 'POST',\r\n    path: '/base/tenant/save'\r\n  },\r\n  {\r\n    name: 'tenant-delete', // 删除\r\n    method: 'POST',\r\n    path: '/base/tenant/delete'\r\n  },\r\n  {\r\n    name: 'baseTenantAdmin-page', // 租户管理员表分页\r\n    method: 'POST',\r\n    path: '/base/baseTenantAdmin/page'\r\n  },\r\n\r\n  {\r\n    name: 'baseTenantAdmin-save', // 保存租户管理员表\r\n    method: 'POST',\r\n    path: '/base/baseTenantAdmin/save'\r\n  },\r\n  {\r\n    name: 'baseTenantAdmin-delete', // 删除\r\n    method: 'OTHER',\r\n    path: '/base/baseTenantAdmin/delete'\r\n  }\r\n];\r\n"], "mappings": "AAAA,eAAe,CACb;EACEA,IAAI,EAAE,aAAa;EAAE;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EAED;EACEF,IAAI,EAAE,aAAa;EAAE;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,qBAAqB;EAC3BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EAAE;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,eAAe;EAAE;EACvBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,sBAAsB;EAAE;EAC9BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EAED;EACEF,IAAI,EAAE,sBAAsB;EAAE;EAC9BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,wBAAwB;EAAE;EAChCC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,CACF", "ignoreList": []}]}